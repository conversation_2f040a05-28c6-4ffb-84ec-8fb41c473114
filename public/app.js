class AuthService {
  constructor() {
    this.isAuthenticated = true; // Access gate remains primary
  }

  async checkAuthStatus() {
    return true;
  }

  async makeAuthenticatedRequest(url, options = {}) {
    // Set appropriate headers based on content type
    let headers = { ...options.headers };

    // Attach JWT if present
    try {
      const token =
        (typeof window !== 'undefined' &&
          (window.__jwt || sessionStorage.getItem('jwt'))) ||
        null;
      if (token && !headers['Authorization']) {
        headers['Authorization'] = `Bearer ${token}`;
      }
    } catch (_) {
      // sessionStorage may be unavailable; ignore
    }

    // For JSON requests, ensure Content-Type is set
    if (
      options.body &&
      typeof options.body === 'string' &&
      !headers['Content-Type']
    ) {
      headers['Content-Type'] = 'application/json';
    }

    const response = await fetch(url, {
      ...options,
      headers,
      credentials: 'include',
    });

    return response;
  }
}

class VoiceLeadApp {
  constructor() {
    this.isRecording = false;
    this.isListening = false; // New: tracks if voice mode is active
    this.mediaRecorder = null;
    this.audioChunks = [];
    this.currentAudio = null;
    this.leadData = {};
    this.conversationHistory = [];
    this.isFirstMessage = true;
    this.currentSessionId = null;
    this.allConversations = [];
    this.userHasInteracted = false;
    this.statusHideTimer = null;
    this.sessionId = Date.now().toString(); // Unique session ID
    this.voiceActivationTimeout = null; // For automatic recording restart
    this.authService = new AuthService(); // Add authentication service
    this.audioContextUnlocked = false; // Track if audio context is unlocked

    this.initializeApp();
  }

  async initializeApp() {
    // Check authentication first
    const isAuthenticated = await this.authService.checkAuthStatus();

    this.initializeElements();
    this.bindEvents();
    this.checkMicrophoneSupport();
    this.checkAudioFormats();
    this.showWelcomeMessage();
    this.startStatusHideTimer();
  }

  initializeElements() {
    this.micButton = document.getElementById('micButton');
    this.micIcon = document.getElementById('micIcon');
    this.micStatus = document.getElementById('micStatus');
    this.statusIndicator = document.getElementById('statusIndicator');
    this.conversationDisplay = document.getElementById('conversationDisplay');
    this.playButton = document.getElementById('playButton');
    this.stopButton = document.getElementById('stopButton');
    this.oldStopButton = document.getElementById('oldStopButton');
    this.audioPlayer = document.getElementById('audioPlayer');
    this.saveLeadBtn = document.getElementById('saveLeadBtn');
    this.exportLeadBtn = document.getElementById('exportLeadBtn');
    this.leadInfo = document.getElementById('leadInfo');
    this.aiSpeakingIndicator = document.getElementById('aiSpeakingIndicator');
    this.textInput = document.getElementById('textInput');
    this.sendButton = document.getElementById('sendButton');
    this.aiThinkingIndicator = document.getElementById('aiThinkingIndicator');

        // Stock preview card elements
        this.stockPreviewCard = document.getElementById('stockPreviewCard');
        this.previewCompanyName = document.getElementById('previewCompanyName');
        this.previewTicker = document.getElementById('previewTicker');
        this.previewPrice = document.getElementById('previewPrice');
        this.previewChange = document.getElementById('previewChange');
        this.previewMarketCap = document.getElementById('previewMarketCap');
        this.previewTarget = document.getElementById('previewTarget');



    // Check if critical elements exist
    if (!this.micButton) {
      console.error('Microphone button element not found!');
      return;
    }
    if (!this.micIcon) {
      console.error('Microphone icon element not found!');
      return;
    }

    // Log successful initialization
    console.log('✅ Microphone button and icon elements found and initialized');
    console.log('Microphone button:', this.micButton);
    console.log('Microphone icon:', this.micIcon);

    // Diagnostic check for button visibility
    this.checkButtonVisibility();

    // Menu elements
    this.burgerMenu = document.getElementById('burgerMenu');
    this.sideMenu = document.getElementById('sideMenu');
    this.menuOverlay = document.getElementById('menuOverlay');
    this.closeMenu = document.getElementById('closeMenu');
    this.menuHistoryBtn = document.getElementById('menuHistoryBtn');
    this.menuExportBtn = document.getElementById('menuExportBtn');
    this.menuClearBtn = document.getElementById('menuClearBtn');

    // History elements
    this.historyModal = document.getElementById('historyModal');
    this.closeHistory = document.getElementById('closeHistory');
    this.historyContent = document.getElementById('historyContent');
  }

  bindEvents() {
    if (this.micButton) {
      this.micButton.addEventListener('click', () => this.toggleRecording());
    }
    if (this.playButton) {
      this.playButton.addEventListener('click', () => this.playLastResponse());
    }
    if (this.stopButton) {
      this.stopButton.addEventListener('click', () => this.stopEverything());
    }
    if (this.oldStopButton) {
      this.oldStopButton.addEventListener('click', () => this.stopAudio());
    }
    if (this.saveLeadBtn) {
      this.saveLeadBtn.addEventListener('click', () => this.saveLead());
    }
    if (this.exportLeadBtn) {
      this.exportLeadBtn.addEventListener('click', () => this.exportLead());
    }

    // Text input event listeners
    if (this.sendButton) {
      this.sendButton.addEventListener('click', () => this.sendTextMessage());
    }
    if (this.textInput) {
      this.textInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
          this.sendTextMessage();
        }
      });
    }

    // Menu event listeners
    this.burgerMenu.addEventListener('click', () => this.toggleMenu());
    this.closeMenu.addEventListener('click', () => this.hideMenu());
    this.menuOverlay.addEventListener('click', () => this.hideMenu());
    this.menuHistoryBtn.addEventListener('click', () => {
      this.hideMenu();
      this.showHistory();
    });
    this.menuExportBtn.addEventListener('click', () => {
      this.hideMenu();
      this.exportConversationHistory();
    });
    this.menuClearBtn.addEventListener('click', () => {
      this.hideMenu();
      this.clearConversationHistory();
    });

    // History modal event listeners
    this.closeHistory.addEventListener('click', () => this.hideHistory());
    this.historyModal.addEventListener('click', (e) => {
      if (e.target === this.historyModal) {
        this.hideHistory();
      }
    });
  }

  checkButtonVisibility() {
    if (!this.micButton) return;

    const rect = this.micButton.getBoundingClientRect();
    const styles = window.getComputedStyle(this.micButton);

    console.log('🔍 Microphone Button Diagnostic:');
    console.log('- Position:', rect);
    console.log('- Display:', styles.display);
    console.log('- Visibility:', styles.visibility);
    console.log('- Opacity:', styles.opacity);
    console.log('- Z-index:', styles.zIndex);
    console.log('- Disabled:', this.micButton.disabled);
    console.log('- Parent element:', this.micButton.parentElement);

    // Check if button is in viewport
    const isInViewport =
      rect.top >= 0 &&
      rect.left >= 0 &&
      rect.bottom <= window.innerHeight &&
      rect.right <= window.innerWidth;
    console.log('- In viewport:', isInViewport);

    // Check if button is actually visible
    const isVisible =
      rect.width > 0 &&
      rect.height > 0 &&
      styles.display !== 'none' &&
      styles.visibility !== 'hidden' &&
      parseFloat(styles.opacity) > 0;
    console.log('- Actually visible:', isVisible);
  }

  checkAudioFormats() {
    console.log('=== Audio Format Support Check ===');
    const formats = [
      'audio/webm',
      'audio/webm;codecs=opus',
      'audio/mp4',
      'audio/ogg',
      'audio/wav',
      'audio/mpeg',
    ];

    formats.forEach((format) => {
      const supported = MediaRecorder.isTypeSupported(format);
      console.log(
        `${format}: ${supported ? '✓ Supported' : '✗ Not supported'}`
      );
    });
    console.log('=== End Audio Format Check ===');
  }

  async checkMicrophoneSupport() {
    console.log('🎤 Checking microphone support...');
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      stream.getTracks().forEach((track) => track.stop());
      console.log('✅ Microphone access granted');
      this.updateStatus('Microphone ready - click to start');
    } catch (error) {
      console.error('❌ Microphone error:', error);
      if (this.micButton) {
        this.micButton.disabled = true;
        console.log('🔒 Microphone button disabled due to error');
      }
      console.error('Microphone error:', error);

      let errorMessage =
        'Microphone access is required for voice functionality.';
      let details = error.message;

      if (error.name === 'NotAllowedError') {
        errorMessage =
          'Microphone access was denied. Please allow microphone access and refresh the page.';
        details =
          "Click the microphone icon in your browser's address bar to allow access.";
      } else if (error.name === 'NotFoundError') {
        errorMessage =
          'No microphone found. Please connect a microphone and refresh the page.';
      }

      this.showError('Microphone Not Available', errorMessage, details);
    }
  }

  startStatusHideTimer() {
    // Hide status indicator after 5 seconds
    this.statusHideTimer = setTimeout(() => {
      this.statusIndicator.classList.add('hidden');
    }, 5000);
  }

  showWelcomeMessage() {
    // Show initial AI greeting (but don't auto-play audio)
    setTimeout(() => {
      const welcomeText =
        'Hello, James. The Market has been noisy - lets catch you up.';
      this.addMessage(welcomeText, 'ai');
      // Don't auto-play the welcome message - wait for user interaction
    }, 1000);
  }

  async toggleRecording() {
    // Mark that user has interacted with the page
    this.userHasInteracted = true;

    // Create audio context for auto-play (must be done in user interaction)
    this.prepareAudioContext();

    if (this.isListening) {
      // Turn OFF voice mode
      this.stopVoiceMode();
    } else {
      // Turn ON voice mode
      await this.startVoiceMode();
    }
  }

  prepareAudioContext() {
    // Create and prepare an audio element during user interaction
    // This ensures we have a "blessed" audio element that can auto-play
    if (!this.audioContextUnlocked) {
      // Create a reusable audio element in the user interaction context
      this.audioPlayer = document.getElementById('audioPlayer') || new Audio();

      // Play a silent sound to unlock audio context
      const silentAudio = new Audio(
        'data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT'
      );
      silentAudio.volume = 0;
      silentAudio.play().catch(() => {
        // Ignore errors for silent audio
      });

      this.audioContextUnlocked = true;
      console.log('Audio context unlocked for auto-play');
    }
  }

  prepareAudioForTTS() {
    // Create a "blessed" audio element during active user interaction
    // This element will be able to auto-play TTS responses
    this.ttsAudioElement = new Audio();
    this.ttsAudioElement.preload = 'auto';

    // Set up event handlers
    this.ttsAudioElement.addEventListener('ended', () => {
      if (this.isListening) {
        // If in listening mode, restart recording after AI finishes speaking
        this.updateUI('listening');
        this.showAIThinking('Listening...');
        this.voiceActivationTimeout = setTimeout(async () => {
          if (this.isListening && !this.isRecording) {
            await this.startRecording();
          }
        }, 1000);
      } else {
        this.hideAIThinking();
        this.updateUI('ready');
      }
    });

    this.ttsAudioElement.addEventListener('error', (e) => {
      console.error('TTS Audio error:', e);
      this.handleAutoPlayBlocked();
    });

    console.log('🎵 TTS audio element prepared for auto-play');
  }

  async startVoiceMode() {
    this.isListening = true;
    this.updateUI('listening');
    this.updateStatus('Voice mode ON - Listening for speech...');

    // Show blob animation when voice mode starts
    this.showAIThinking('Listening...');

    // Start the first recording session
    await this.startRecording();
  }

  stopVoiceMode() {
    this.isListening = false;

    // Clear any pending restart timeout
    if (this.voiceActivationTimeout) {
      clearTimeout(this.voiceActivationTimeout);
      this.voiceActivationTimeout = null;
    }

    // Stop current recording if active
    if (this.isRecording) {
      this.stopRecording();
    }

    // Hide blob animation when voice mode stops
    this.hideAIThinking();

    this.updateUI('ready');
    this.updateStatus('Voice mode OFF - Click to activate');
  }

  async startRecording() {
    try {
      // Pre-create audio element for TTS response during user interaction
      this.prepareAudioForTTS();

      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          sampleRate: 44100, // Changed to more compatible sample rate
          channelCount: 1,
          echoCancellation: true,
          noiseSuppression: true,
        },
      });

      // Try to use a supported audio format with better compatibility
      let options = {};
      let mimeType = '';

      // Check for supported formats in order of preference
      if (MediaRecorder.isTypeSupported('audio/webm')) {
        options = { mimeType: 'audio/webm' };
        mimeType = 'audio/webm';
      } else if (MediaRecorder.isTypeSupported('audio/mp4')) {
        options = { mimeType: 'audio/mp4' };
        mimeType = 'audio/mp4';
      } else if (MediaRecorder.isTypeSupported('audio/ogg')) {
        options = { mimeType: 'audio/ogg' };
        mimeType = 'audio/ogg';
      } else {
        // Fallback to default (usually works)
        console.log('Using default MediaRecorder format');
        mimeType = 'audio/wav'; // Default assumption
      }

      console.log('Using audio format:', mimeType);

      this.mediaRecorder = new MediaRecorder(stream, options);
      this.audioChunks = [];
      this.recordedMimeType = mimeType; // Store for later use

      this.mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          this.audioChunks.push(event.data);
        }
      };

      this.mediaRecorder.onstop = () => {
        this.processRecording();
      };

            this.mediaRecorder.start(1000); // Record in 1-second chunks
            this.isRecording = true;

      this.updateUI('recording');
      this.updateStatus('Listening... Click again to stop');
    } catch (error) {
      console.error('Recording error:', error);
      this.showError(
        'Recording Failed',
        'Could not start recording. Please check microphone permissions.',
        `Error: ${error.message}\nMake sure your browser has microphone access.`
      );

      // If in listening mode, try to restart after error
      if (this.isListening) {
        this.voiceActivationTimeout = setTimeout(async () => {
          if (this.isListening && !this.isRecording) {
            console.log('Retrying recording after error...');
            await this.startRecording();
          }
        }, 5000); // 5 second delay before retry
      }
    }
  }

    stopRecording() {
        if (this.mediaRecorder && this.isRecording) {
            this.mediaRecorder.stop();
            this.mediaRecorder.stream.getTracks().forEach(track => track.stop());
            this.isRecording = false;

            this.updateUI('processing');
            this.updateStatus('Processing your message...');
        }
    }

  async processRecording() {
    try {
      // Check if we have audio data
      if (this.audioChunks.length === 0) {
        throw new Error('No audio data recorded');
      }

      // Determine the correct MIME type based on what was recorded
      let mimeType = this.recordedMimeType || 'audio/wav';
      if (this.mediaRecorder && this.mediaRecorder.mimeType) {
        mimeType = this.mediaRecorder.mimeType;
      }

      console.log('Processing audio with MIME type:', mimeType);
      console.log('Audio chunks:', this.audioChunks.length);

      const audioBlob = new Blob(this.audioChunks, { type: mimeType });

      // Check blob size
      if (audioBlob.size === 0) {
        throw new Error('Audio blob is empty');
      }

      console.log('Audio blob size:', audioBlob.size, 'bytes');

      // Send to speech-to-text API
      const transcription = await this.speechToText(audioBlob);

      if (transcription) {
        this.addMessage(transcription, 'user');

        // Send to AI for response
        const aiResponse = await this.getAIResponse(transcription);

        if (aiResponse) {
          this.addMessage(aiResponse, 'ai');

          // Convert AI response to speech
          await this.textToSpeech(aiResponse);

          // Extract lead information
          this.extractLeadInfo(transcription, aiResponse);
        }
      }

      // If still in listening mode, restart recording after a brief pause
      if (this.isListening) {
        this.updateUI('listening');
        this.updateStatus('Voice mode ON - Listening for speech...');

        // Wait a moment before restarting recording to allow for AI response
        this.voiceActivationTimeout = setTimeout(async () => {
          if (this.isListening && !this.isRecording) {
            await this.startRecording();
          }
        }, 2000); // 2 second delay
      } else {
        this.updateUI('ready');
        this.updateStatus('Ready for next message');
      }
    } catch (error) {
      console.error('Processing error:', error);
      this.showError(
        'Processing Failed',
        'Could not process your voice message. Please try again.',
        `Error: ${error.message}\nCheck your microphone and internet connection.`
      );

      // If still in listening mode, try to restart recording
      if (this.isListening) {
        this.updateUI('listening');
        this.voiceActivationTimeout = setTimeout(async () => {
          if (this.isListening && !this.isRecording) {
            await this.startRecording();
          }
        }, 3000); // 3 second delay after error
      } else {
        this.updateUI('ready');
      }
    }
  }

  async speechToText(audioBlob) {
    try {
      // Update animation to show speech processing
      this.showAIThinking('Processing speech...');

      const formData = new FormData();

      // Determine file extension based on MIME type
      let filename = 'recording.wav';
      let mimeType = audioBlob.type;

      console.log('Audio blob MIME type:', mimeType);

      if (mimeType.includes('webm')) {
        filename = 'recording.webm';
      } else if (mimeType.includes('mp4')) {
        filename = 'recording.mp4';
      } else if (mimeType.includes('ogg')) {
        filename = 'recording.ogg';
      } else if (mimeType.includes('mpeg')) {
        filename = 'recording.mp3';
      } else {
        // Default to wav for unknown types
        filename = 'recording.wav';
      }

      console.log('Sending audio file:', filename, 'Size:', audioBlob.size);
      formData.append('audio', audioBlob, filename);

      const response = await this.authService.makeAuthenticatedRequest(
        '/api/speech-to-text',
        {
          method: 'POST',
          body: formData,
          // Don't set headers for FormData - let browser handle Content-Type with boundary
        }
      );

      console.log('Speech-to-text response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Speech-to-text HTTP error:', errorText);
        throw new Error(
          `HTTP error! status: ${response.status} - ${errorText}`
        );
      }

      const result = await response.json();
      console.log('Speech-to-text result:', result);

      if (result.success && result.transcription) {
        console.log('Transcription successful:', result.transcription);
        return result.transcription;
      } else {
        throw new Error(
          result.error || result.details || 'Failed to transcribe audio'
        );
      }
    } catch (error) {
      console.error('Speech-to-text error:', error);
      this.showError(
        'Speech Recognition Failed',
        'Could not convert your speech to text. Please try again.',
        `Error: ${error.message}\nThis might be due to audio format issues or network problems.`
      );
      return null;
    } finally {
      // Don't hide animation here - let it continue to AI response
    }
  }

  async sendTextMessage() {
    const message = this.textInput.value.trim();
    if (!message) return;

    // Mark that user has interacted with the page
    this.userHasInteracted = true;

    // Create audio context for auto-play (must be done in user interaction)
    this.prepareAudioContext();

    // Clear the input
    this.textInput.value = '';

    // Disable send button temporarily
    this.sendButton.disabled = true;

    try {
      // Add user message to conversation
      this.addMessage(message, 'user');

      // Get AI response
      const aiResponse = await this.getAIResponse(message);

      if (aiResponse) {
        // Add AI response to conversation
        this.addMessage(aiResponse, 'ai');

        // Always play TTS response for text input (users expect voice response)
        await this.textToSpeech(aiResponse);
      }
    } catch (error) {
      console.error('Error sending text message:', error);
      this.showError(
        'Message Failed',
        'Could not send your message. Please try again.',
        `Error: ${error.message}`
      );
    } finally {
      // Re-enable send button
      this.sendButton.disabled = false;
    }
  }

  async getAIResponse(message) {
    try {
      // Update animation to show AI thinking
      this.showAIThinking('Sarah is thinking...');

      const response = await this.authService.makeAuthenticatedRequest(
        '/api/chat',
        {
          method: 'POST',
          body: JSON.stringify({
            message,
            context: 'stock_business_lead',
            leadData: this.leadData,
            conversationHistory: this.conversationHistory,
            sessionId: this.sessionId,
          }),
        }
      );

      if (!response.ok) {
        console.error('AI response error:', response);
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      if (result.success && result.response) {
        // Add to conversation history
        this.conversationHistory.push(
          { role: 'user', content: message },
          { role: 'assistant', content: result.response }
        );

        // Keep conversation history manageable (last 10 exchanges)
        if (this.conversationHistory.length > 20) {
          this.conversationHistory = this.conversationHistory.slice(-20);
        }

        // Clean markdown formatting from AI response
        const cleanResponse = this.stripMarkdownFormatting(result.response);
        return cleanResponse;
      } else {
        throw new Error(result.error || 'Failed to get AI response');
      }
    } catch (error) {
      console.error('AI response error:', error);
      this.showError(
        'AI Response Failed',
        'Could not get a response from the AI. Please try again.',
        `Error: ${error.message}\nThis might be due to API issues or network problems.`
      );
      return 'Sorry, I encountered an error. Please try again.';
    } finally {
      // Don't hide animation here - let it continue to speech
    }
  }

  /**
   * Strip markdown formatting from text
   * @param {string} text - Text with potential markdown formatting
   * @returns {string} Clean text without markdown
   */
  stripMarkdownFormatting(text) {
    if (!text || typeof text !== 'string') {
      return text;
    }

    return text
      // Remove bold formatting **text** and __text__
      .replace(/\*\*(.*?)\*\*/g, '$1')
      .replace(/__(.*?)__/g, '$1')
      // Remove italic formatting *text* and _text_
      .replace(/\*(.*?)\*/g, '$1')
      .replace(/_(.*?)_/g, '$1')
      // Remove strikethrough ~~text~~
      .replace(/~~(.*?)~~/g, '$1')
      // Remove inline code `text`
      .replace(/`(.*?)`/g, '$1')
      // Remove headers ### text
      .replace(/^#{1,6}\s+/gm, '')
      // Remove list markers - and *
      .replace(/^[\s]*[-*+]\s+/gm, '')
      // Remove numbered list markers
      .replace(/^[\s]*\d+\.\s+/gm, '')
      // Clean up any remaining multiple spaces
      .replace(/\s+/g, ' ')
      .trim();
  }

  async textToSpeech(text) {
    try {
      // Update status to show speech generation
      this.updateStatus('Generating speech...', false);

      const response = await this.authService.makeAuthenticatedRequest(
        '/api/text-to-speech',
        {
          method: 'POST',
          body: JSON.stringify({ text }),
        }
      );

      if (response.ok) {
        const audioBlob = await response.blob();
        const audioUrl = URL.createObjectURL(audioBlob);

        this.currentAudio = audioUrl;

        // Create a fresh audio element for this response
        const audioElement = new Audio();
        audioElement.src = audioUrl;
        audioElement.preload = 'auto';

        // Store references
        this.currentAudio = audioUrl;
        this.audioPlayer = audioElement;
        this.playButton.disabled = false;
        this.stopButton.disabled = false;

                // Set up event handlers for this audio element
                audioElement.addEventListener('ended', () => {
                    console.log('🎵 Audio playback ended');

                    // Hide stock preview when audio ends
                    this.hideStockPreview();

                    if (this.isListening) {
                        // If in listening mode, restart recording after AI finishes speaking
                        this.updateUI('listening');
                        this.showAIThinking('Listening...');
                        this.voiceActivationTimeout = setTimeout(async () => {
                            if (this.isListening && !this.isRecording) {
                                await this.startRecording();
                            }
                        }, 1000);
                    } else {
                        this.hideAIThinking();
                        this.updateUI('ready');
                    }
                });

        audioElement.addEventListener('error', (e) => {
          console.error('❌ Audio playback error:', e);
          this.handleAutoPlayBlocked();
        });

        audioElement.addEventListener('loadstart', () => {
          console.log('🎵 Audio loading started');
        });

        audioElement.addEventListener('canplay', () => {
          console.log('🎵 Audio can start playing');
        });

        // Force auto-play - this should work since user just interacted
        try {
          this.updateUI('speaking');
          this.showAIThinking('Sarah is speaking...');

          console.log('🎵 Attempting auto-play...');

          // Try to play immediately
          const playPromise = audioElement.play();

          if (playPromise !== undefined) {
            playPromise.then(async () => {
              console.log('✅ Audio auto-play successful!');
            }).catch((error) => {
              console.log('❌ Auto-play blocked:', error.name, error.message);
              this.handleAutoPlayBlocked();
            });
          } else {
            console.log('✅ Audio play() returned undefined - likely successful');
          }
        } catch (error) {
          console.log('❌ Audio setup error:', error.message);
          this.handleAutoPlayBlocked();
        }
      } else {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
    } catch (error) {
      console.error('Text-to-speech error:', error);
      this.showError(
        'Speech Generation Failed',
        'Could not convert the AI response to speech, but you can still read the text.',
        `Error: ${error.message}\nThe conversation can continue without audio.`
      );
    }
  }

  addMessage(text, sender) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${sender}-message`;
    messageDiv.textContent = text;

    // Remove welcome message if it exists
    const welcomeMessage =
      this.conversationDisplay.querySelector('.welcome-message');
    if (welcomeMessage) {
      welcomeMessage.remove();
    }

    this.conversationDisplay.appendChild(messageDiv);

    // If this is an AI message, check for stocks and add inline cards
    if (sender === 'ai') {
      this.addInlineStockCards(text, messageDiv);
    }

    this.conversationDisplay.scrollTop = this.conversationDisplay.scrollHeight;
  }

  async addInlineStockCards(text, messageDiv) {
    try {
      // Detect stocks in the AI response
      const detectedStocks = await this.detectStocksInResponse(text);

      if (detectedStocks.length > 0) {
        console.log(`📊 Detected ${detectedStocks.length} stock(s) in AI response:`,
          detectedStocks.map(s => s.ticker).join(', '));

        // Always create carousel for consistency, even with single stock
        const carousel = this.createStockCarousel(detectedStocks);
        messageDiv.appendChild(carousel);
      }
    } catch (error) {
      console.error('Error adding inline stock cards:', error);
    }
  }

  createInlineStockCard(stockData) {
    const cardDiv = document.createElement('div');
    cardDiv.className = 'inline-stock-card';

    // Generate community member data
    const communityMember = this.generateCommunityMember();
    const investmentThesis = this.generateInvestmentThesis(stockData.ticker);

    cardDiv.innerHTML = `
      <div class="inline-stock-content">
        <div class="stock-header">
          <span class="company-name">${stockData.companyName}</span>
          <span class="ticker-symbol">${stockData.ticker}</span>
        </div>

        <div class="community-insight">
          <div class="profile-section">
            <div class="profile-avatar">
              <img src="${communityMember.avatar}" alt="${communityMember.name}" />
            </div>
            <span class="member-name">${communityMember.name}</span>
          </div>
        </div>

        <div class="price-section">
          <span class="price-label">Price</span>
          <div class="price-info">
            <span class="current-price">$${stockData.price}</span>
            <span class="price-change ${stockData.change >= 0 ? 'positive' : 'negative'}">
              ${stockData.change >= 0 ? '+' : ''}${stockData.changePercent}%
            </span>
          </div>
        </div>

        <div class="metrics-row">
          <div class="metric-item">
            <span class="metric-label">Market Cap</span>
            <span class="metric-value">${stockData.marketCap}</span>
          </div>
          <div class="metric-item">
            <span class="metric-label">Price Target</span>
            <span class="metric-value">$${stockData.priceTarget} <span class="target-upside">+60%</span></span>
          </div>
        </div>

        <div class="thesis-section">
          <span class="thesis-label">Thesis</span>
          <p class="thesis-text">${investmentThesis}</p>
        </div>

        <div class="timestamp">
          <span class="pricing-note">Pricing delayed 20 minutes. ${new Date().toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric'
          })} ${new Date().toLocaleTimeString('en-US', {
            hour: 'numeric',
            minute: '2-digit',
            hour12: true
          })}</span>
        </div>
      </div>
    `;

    return cardDiv;
  }

  createStockCarousel(stocksArray) {
    const carouselContainer = document.createElement('div');
    carouselContainer.className = 'stock-carousel-container';

    // Create carousel wrapper
    const carouselWrapper = document.createElement('div');
    carouselWrapper.className = 'stock-carousel-wrapper';

    // Create carousel track
    const carouselTrack = document.createElement('div');
    carouselTrack.className = 'stock-carousel-track';

    // Add stock cards to track
    stocksArray.forEach((stock, index) => {
      const stockCard = this.createInlineStockCard(stock);
      stockCard.classList.add('carousel-card');
      stockCard.setAttribute('data-index', index);
      carouselTrack.appendChild(stockCard);
    });

    // Create dot indicators
    const dotsContainer = document.createElement('div');
    dotsContainer.className = 'carousel-dots';

    stocksArray.forEach((_, index) => {
      const dot = document.createElement('button');
      dot.className = `carousel-dot ${index === 0 ? 'active' : ''}`;
      dot.setAttribute('data-index', index);
      dot.addEventListener('click', () => this.goToSlide(carouselContainer, index));
      dotsContainer.appendChild(dot);
    });

    // Assemble carousel
    carouselWrapper.appendChild(carouselTrack);
    carouselContainer.appendChild(carouselWrapper);
    carouselContainer.appendChild(dotsContainer);

    // Initialize carousel state
    carouselContainer.setAttribute('data-current-slide', '0');
    carouselContainer.setAttribute('data-total-slides', stocksArray.length);

    // Add touch/swipe support
    this.addCarouselTouchSupport(carouselContainer);

    // Start auto-advance
    this.startCarouselAutoAdvance(carouselContainer);

    return carouselContainer;
  }

  generateCommunityMember() {
    const members = [
      {
        name: "Alex Chen",
        avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face&auto=format"
      },
      {
        name: "Sarah Kim",
        avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face&auto=format"
      },
      {
        name: "Marcus Johnson",
        avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face&auto=format"
      },
      {
        name: "Emma Rodriguez",
        avatar: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face&auto=format"
      },
      {
        name: "David Park",
        avatar: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=100&h=100&fit=crop&crop=face&auto=format"
      }
    ];

    return members[Math.floor(Math.random() * members.length)];
  }

  generateInvestmentThesis(ticker) {
    const theses = {
      'AAPL': [
        "Strong services revenue growth with expanding ecosystem lock-in.",
        "AI integration across devices driving upgrade cycles.",
        "Vision Pro represents next major platform opportunity."
      ],
      'TSLA': [
        "FSD breakthrough could unlock massive autonomous value.",
        "Energy storage business scaling rapidly with grid demand.",
        "Manufacturing efficiency improvements driving margin expansion."
      ],
      'MSFT': [
        "Azure cloud growth accelerating with AI workloads.",
        "Copilot integration creating new revenue streams.",
        "Enterprise software moat strengthening with AI capabilities."
      ],
      'GOOGL': [
        "Search dominance enhanced by AI integration.",
        "Cloud infrastructure gaining share in enterprise market.",
        "YouTube's creator economy driving engagement growth."
      ],
      'AMZN': [
        "AWS margins expanding as scale advantages compound.",
        "Advertising business becoming major profit driver.",
        "Logistics network creating competitive moats."
      ],
      'NVDA': [
        "AI chip demand exceeding supply for foreseeable future.",
        "Data center transformation driving multi-year growth.",
        "Software ecosystem creating recurring revenue streams."
      ],
      'META': [
        "Metaverse investments positioning for next computing platform.",
        "AI-driven ad targeting improving monetization efficiency.",
        "Reality Labs breakthrough could unlock new markets."
      ]
    };

    const tickerTheses = theses[ticker] || [
      "Strong fundamentals with compelling growth prospects.",
      "Market leadership position in expanding sector.",
      "Innovative product pipeline driving future growth."
    ];

    return tickerTheses[Math.floor(Math.random() * tickerTheses.length)];
  }

  goToSlide(carouselContainer, slideIndex) {
    const track = carouselContainer.querySelector('.stock-carousel-track');
    const dots = carouselContainer.querySelectorAll('.carousel-dot');
    const totalSlides = parseInt(carouselContainer.getAttribute('data-total-slides'));

    // Ensure slideIndex is within bounds
    slideIndex = Math.max(0, Math.min(slideIndex, totalSlides - 1));

    // Update track position
    const translateX = -slideIndex * 100;
    track.style.transform = `translateX(${translateX}%)`;

    // Update dots
    dots.forEach((dot, index) => {
      dot.classList.toggle('active', index === slideIndex);
    });

    // Update current slide
    carouselContainer.setAttribute('data-current-slide', slideIndex);

    // Reset auto-advance timer
    this.resetCarouselAutoAdvance(carouselContainer);
  }

  addCarouselTouchSupport(carouselContainer) {
    const track = carouselContainer.querySelector('.stock-carousel-track');
    let startX = 0;
    let currentX = 0;
    let isDragging = false;

    // Touch events
    track.addEventListener('touchstart', (e) => {
      startX = e.touches[0].clientX;
      isDragging = true;
      track.style.transition = 'none';
    });

    track.addEventListener('touchmove', (e) => {
      if (!isDragging) return;
      e.preventDefault();
      currentX = e.touches[0].clientX;
      const diffX = currentX - startX;
      const currentSlide = parseInt(carouselContainer.getAttribute('data-current-slide'));
      const translateX = -currentSlide * 100 + (diffX / track.offsetWidth) * 100;
      track.style.transform = `translateX(${translateX}%)`;
    });

    track.addEventListener('touchend', () => {
      if (!isDragging) return;
      isDragging = false;
      track.style.transition = 'transform 0.3s ease';

      const diffX = currentX - startX;
      const threshold = 50; // Minimum swipe distance
      const currentSlide = parseInt(carouselContainer.getAttribute('data-current-slide'));

      if (Math.abs(diffX) > threshold) {
        if (diffX > 0) {
          // Swipe right - go to previous slide
          this.goToSlide(carouselContainer, currentSlide - 1);
        } else {
          // Swipe left - go to next slide
          this.goToSlide(carouselContainer, currentSlide + 1);
        }
      } else {
        // Snap back to current slide
        this.goToSlide(carouselContainer, currentSlide);
      }
    });

    // Mouse events for desktop
    track.addEventListener('mousedown', (e) => {
      startX = e.clientX;
      isDragging = true;
      track.style.transition = 'none';
      e.preventDefault();
    });

    document.addEventListener('mousemove', (e) => {
      if (!isDragging) return;
      currentX = e.clientX;
      const diffX = currentX - startX;
      const currentSlide = parseInt(carouselContainer.getAttribute('data-current-slide'));
      const translateX = -currentSlide * 100 + (diffX / track.offsetWidth) * 100;
      track.style.transform = `translateX(${translateX}%)`;
    });

    document.addEventListener('mouseup', () => {
      if (!isDragging) return;
      isDragging = false;
      track.style.transition = 'transform 0.3s ease';

      const diffX = currentX - startX;
      const threshold = 50;
      const currentSlide = parseInt(carouselContainer.getAttribute('data-current-slide'));

      if (Math.abs(diffX) > threshold) {
        if (diffX > 0) {
          this.goToSlide(carouselContainer, currentSlide - 1);
        } else {
          this.goToSlide(carouselContainer, currentSlide + 1);
        }
      } else {
        this.goToSlide(carouselContainer, currentSlide);
      }
    });
  }

  startCarouselAutoAdvance(carouselContainer) {
    const totalSlides = parseInt(carouselContainer.getAttribute('data-total-slides'));

    // Don't auto-advance if there's only one slide
    if (totalSlides <= 1) return;

    const autoAdvanceInterval = setInterval(() => {
      const currentSlide = parseInt(carouselContainer.getAttribute('data-current-slide'));
      const nextSlide = (currentSlide + 1) % totalSlides;
      this.goToSlide(carouselContainer, nextSlide);
    }, 4000); // 4 seconds

    // Store interval ID for cleanup
    carouselContainer.setAttribute('data-auto-advance-id', autoAdvanceInterval);
  }

  resetCarouselAutoAdvance(carouselContainer) {
    // Clear existing interval
    const intervalId = carouselContainer.getAttribute('data-auto-advance-id');
    if (intervalId) {
      clearInterval(parseInt(intervalId));
    }

    // Start new interval
    this.startCarouselAutoAdvance(carouselContainer);
  }

  extractLeadInfo(userMessage, aiResponse) {
    // Simple lead information extraction
    // In a real implementation, this would be more sophisticated
    // aiResponse could be used for more advanced extraction in the future

    const nameMatch =
      userMessage.match(/my name is (\w+)/i) || userMessage.match(/i'm (\w+)/i);
    if (nameMatch) {
      this.leadData.name = nameMatch[1];
    }

    const emailMatch = userMessage.match(/[\w.-]+@[\w.-]+\.\w+/);
    if (emailMatch) {
      this.leadData.email = emailMatch[0];
    }

    const phoneMatch = userMessage.match(/(\d{3}[-.]?\d{3}[-.]?\d{4})/);
    if (phoneMatch) {
      this.leadData.phone = phoneMatch[1];
    }

    // Update lead display
    this.updateLeadDisplay();
  }

  updateLeadDisplay() {
    if (Object.keys(this.leadData).length > 0) {
      let leadHtml = '<div class="lead-details">';

      if (this.leadData.name) {
        leadHtml += `<p><strong>Name:</strong> ${this.leadData.name}</p>`;
      }
      if (this.leadData.email) {
        leadHtml += `<p><strong>Email:</strong> ${this.leadData.email}</p>`;
      }
      if (this.leadData.phone) {
        leadHtml += `<p><strong>Phone:</strong> ${this.leadData.phone}</p>`;
      }

      leadHtml += '</div>';
      this.leadInfo.innerHTML = leadHtml;

      this.saveLeadBtn.disabled = false;
      this.exportLeadBtn.disabled = false;
    }
  }

  updateUI(state) {
    // Check if critical elements exist before updating UI
    if (!this.micButton || !this.micIcon) {
      console.error('Critical UI elements not found, cannot update UI');
      return;
    }

    switch (state) {
      case 'listening':
        this.micButton.classList.add('listening');
        this.micButton.classList.remove('recording');
        this.micIcon.className = 'fas fa-microphone-slash';
        if (this.micStatus) {
          this.micStatus.textContent = 'Voice ON - Click to turn OFF';
        }
        if (this.stopButton) {
          this.stopButton.style.display = 'flex';
        }
        this.hideAISpeaking(); // Hide blob animation
        break;
      case 'recording':
        this.micButton.classList.add('recording');
        this.micButton.classList.remove('listening');
        this.micIcon.className = 'fas fa-stop';
        if (this.micStatus) {
          this.micStatus.textContent = 'Recording...';
        }
        if (this.stopButton) {
          this.stopButton.style.display = 'flex';
        }
        this.hideAISpeaking(); // Hide blob animation
        break;
      case 'processing':
        this.micButton.classList.remove('recording', 'listening');
        this.micIcon.className = 'fas fa-spinner fa-spin';
        if (this.micStatus) {
          this.micStatus.textContent = 'Processing...';
        }
        if (this.stopButton) {
          this.stopButton.style.display = 'flex';
        }
        this.hideAISpeaking(); // Hide blob animation
        break;
      case 'speaking':
        this.micButton.classList.remove('recording');
        if (this.isListening) {
          this.micButton.classList.add('listening');
          this.micIcon.className = 'fas fa-microphone-slash';
          if (this.micStatus) {
            this.micStatus.textContent = 'Voice ON - AI speaking...';
          }
        } else {
          this.micIcon.className = 'fas fa-microphone';
          if (this.micStatus) {
            this.micStatus.textContent = 'AI is speaking...';
          }
        }
        if (this.stopButton) {
          this.stopButton.style.display = 'flex';
        }
        this.showAISpeaking(); // Use blob animation for speaking
        break;
      case 'ready':
        this.micButton.classList.remove('recording', 'listening');
        this.micIcon.className = 'fas fa-microphone';
        if (this.micStatus) {
          this.micStatus.textContent = 'Click to turn voice ON';
        }
        if (this.stopButton) {
          this.stopButton.style.display = 'none';
        }
        this.hideAISpeaking(); // Hide blob animation
        break;
    }
  }

  updateStatus(message, isError = false) {
    const statusText = this.statusIndicator.querySelector('.status-text');
    statusText.textContent = message;

    // Clear any existing hide timer
    if (this.statusHideTimer) {
      clearTimeout(this.statusHideTimer);
    }

    // Show the status indicator
    this.statusIndicator.classList.remove('hidden');

    // Add error styling if it's an error
    if (isError) {
      this.statusIndicator.style.background = 'rgba(220, 53, 69, 0.9)';
      statusText.style.color = '#ffffff';

      // Hide error status after 3 seconds
      this.statusHideTimer = setTimeout(() => {
        this.statusIndicator.classList.add('hidden');
      }, 3000);
    } else {
      this.statusIndicator.style.background = 'rgba(0, 0, 0, 0.7)';
      statusText.style.color = '#00d4aa';

      // Hide normal status after 2 seconds
      this.statusHideTimer = setTimeout(() => {
        this.statusIndicator.classList.add('hidden');
      }, 2000);
    }
  }

  showError(title, message, details = null) {
    // Create error message in conversation
    const errorDiv = document.createElement('div');
    errorDiv.className = 'message error-message';
    errorDiv.innerHTML = `
            <div class="error-content">
                <strong>❌ ${title}</strong>
                <p>${message}</p>
                ${
                  details
                    ? `<details><summary>Technical Details</summary><pre>${details}</pre></details>`
                    : ''
                }
            </div>
        `;

    // Remove welcome message if it exists
    const welcomeMessage =
      this.conversationDisplay.querySelector('.welcome-message');
    if (welcomeMessage) {
      welcomeMessage.remove();
    }

    this.conversationDisplay.appendChild(errorDiv);
    this.conversationDisplay.scrollTop = this.conversationDisplay.scrollHeight;

    // Also update status
    this.updateStatus(`Error: ${title}`, true);
  }

  handleAutoPlayBlocked() {
    // Show a visual indicator that audio is ready
    this.updateStatus('🔊 Audio ready - click to play', false);
    this.updateUI('ready');

    // Add a temporary play button to the latest AI message
    this.addPlayButtonToLatestMessage();

    // Also show a notification
    console.log(
      '💡 Tip: Click the play button or enable auto-play in browser settings'
    );
  }

  addPlayButtonToLatestMessage() {
    // Find the latest AI message and add a play button if it doesn't have one
    const messages = this.conversationDisplay.querySelectorAll('.ai-message');
    const latestMessage = messages[messages.length - 1];

    if (latestMessage && !latestMessage.querySelector('.play-audio-btn')) {
      const playButton = document.createElement('button');
      playButton.className = 'play-audio-btn';
      playButton.innerHTML = '<i class="fas fa-play"></i> Play Audio';
      playButton.style.cssText = `
                margin-left: 10px;
                padding: 5px 10px;
                background: #007bff;
                color: white;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                font-size: 12px;
            `;

      playButton.onclick = () => {
        this.playLastResponse();
        playButton.remove(); // Remove button after playing
      };

      latestMessage.appendChild(playButton);
    }
  }

  playLastResponse() {
    if (this.currentAudio) {
      // Show speaking animation when manually playing
      this.showAIThinking('Sarah is speaking...');
      this.audioPlayer.play();
      this.stopButton.disabled = false;
    }
  }

  stopAudio() {
    this.audioPlayer.pause();
    this.audioPlayer.currentTime = 0;
    this.oldStopButton.disabled = true;
  }

  stopEverything() {
    // Stop voice mode if active
    if (this.isListening) {
      this.stopVoiceMode();
    }

    // Stop recording if active
    if (this.isRecording) {
      this.stopRecording();
    }

    // Stop audio playback
    this.audioPlayer.pause();
    this.audioPlayer.currentTime = 0;

    // Stop all carousel auto-advance timers
    this.stopAllCarouselTimers();

    // Reset UI to ready state
    this.updateUI('ready');
    this.updateStatus('Stopped');
  }

  stopAllCarouselTimers() {
    // Find all carousel containers and stop their timers
    const carousels = document.querySelectorAll('.stock-carousel-container');
    carousels.forEach(carousel => {
      const intervalId = carousel.getAttribute('data-auto-advance-id');
      if (intervalId) {
        clearInterval(parseInt(intervalId));
        carousel.removeAttribute('data-auto-advance-id');
      }
    });
  }

  async saveLead() {
    try {
      const response = await fetch('/api/save-lead', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(this.leadData),
      });

      if (response.ok) {
        this.updateStatus('Lead saved successfully');
      } else {
        this.updateStatus('Error saving lead');
      }
    } catch (error) {
      console.error('Save lead error:', error);
      this.updateStatus('Error saving lead');
    }
  }

  exportLead() {
    const leadText = JSON.stringify(this.leadData, null, 2);
    const blob = new Blob([leadText], { type: 'application/json' });
    const url = URL.createObjectURL(blob);

    const a = document.createElement('a');
    a.href = url;
    a.download = `lead_${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }

  // AI Animation Methods
  showAIThinking(message = 'Sarah is thinking...') {
    this.aiThinkingIndicator.style.display = 'flex';
    // Update the text message
    const thinkingText =
      this.aiThinkingIndicator.querySelector('.thinking-text');
    if (thinkingText) {
      thinkingText.textContent = message;
    }
    // Set state for styling
    if (message.includes('speaking')) {
      this.aiThinkingIndicator.setAttribute('data-state', 'speaking');
    } else {
      this.aiThinkingIndicator.setAttribute('data-state', 'thinking');
    }
  }

  hideAIThinking() {
    this.aiThinkingIndicator.style.display = 'none';
    // Reset to default message
    const thinkingText =
      this.aiThinkingIndicator.querySelector('.thinking-text');
    if (thinkingText) {
      thinkingText.textContent = 'Sarah is thinking...';
    }
  }

  showAISpeaking() {
    // Use the thinking animation for speaking as well
    this.showAIThinking('Sarah is speaking...');
  }

    hideAISpeaking() {
        this.hideAIThinking();
    }











  // Menu Methods
  toggleMenu() {
    if (this.sideMenu.classList.contains('active')) {
      this.hideMenu();
    } else {
      this.showMenu();
    }
  }

  showMenu() {
    this.sideMenu.classList.add('active');
    this.menuOverlay.classList.add('active');
    this.burgerMenu.classList.add('active');
    document.body.style.overflow = 'hidden'; // Prevent background scrolling
  }

  hideMenu() {
    this.sideMenu.classList.remove('active');
    this.menuOverlay.classList.remove('active');
    this.burgerMenu.classList.remove('active');
    document.body.style.overflow = ''; // Restore scrolling
  }

  // Conversation History Methods
  async showHistory() {
    this.historyModal.style.display = 'flex';
    this.historyContent.innerHTML =
      '<div class="loading">Loading conversations...</div>';

    try {
      const response = await this.authService.makeAuthenticatedRequest(
        '/api/conversation-history'
      );
      const data = await response.json();

      if (data.success && data.conversations && data.conversations.length > 0) {
        this.renderConversationHistory(data.conversations);
      } else {
        this.historyContent.innerHTML =
          '<div class="no-history">No conversation history found.</div>';
      }
    } catch (error) {
      console.error('Error loading conversation history:', error);
      this.historyContent.innerHTML =
        '<div class="no-history">Error loading conversation history.</div>';
    }
  }

  hideHistory() {
    this.historyModal.style.display = 'none';
  }

  renderConversationHistory(conversations) {
    // Group conversations by session or time proximity
    const groupedConversations = this.groupConversations(conversations);

    // Store for access by continue conversation function
    this.allConversations = groupedConversations;

    let html = '<div class="conversation-list">';

    groupedConversations.forEach((group) => {
      const startDate = new Date(group.startTime);
      const formattedDate = startDate.toLocaleDateString();
      const formattedTime = startDate.toLocaleTimeString([], {
        hour: '2-digit',
        minute: '2-digit',
      });

      html += `
                <div class="conversation-group" data-session-id="${
                  group.sessionId
                }">
                    <div class="conversation-summary" onclick="app.toggleConversationDetails('${
                      group.sessionId
                    }')">
                        <div class="conversation-header">
                            <h4 class="conversation-title">${group.title}</h4>
                            <div class="conversation-meta">
                                <span class="conversation-date">${formattedDate} at ${formattedTime}</span>
                                <span class="message-count">${
                                  group.messages.length
                                } messages</span>
                            </div>
                        </div>
                        <div class="conversation-preview">${this.escapeHtml(
                          group.preview
                        )}</div>
                        <div class="expand-icon">
                            <i class="fas fa-chevron-down"></i>
                        </div>
                    </div>
                    <div class="conversation-details" id="details-${
                      group.sessionId
                    }" style="display: none;">
                        ${this.renderConversationMessages(group.messages)}
                        <div class="conversation-actions">
                            <button class="continue-btn" onclick="app.continueConversation('${
                              group.sessionId
                            }')">
                                <i class="fas fa-play"></i> Continue Conversation
                            </button>
                        </div>
                    </div>
                </div>
            `;
    });

    html += '</div>';
    this.historyContent.innerHTML = html;
  }

  groupConversations(conversations) {
    // Sort conversations by timestamp
    const sorted = conversations.sort(
      (a, b) => new Date(a.timestamp) - new Date(b.timestamp)
    );

    const groups = new Map();

    sorted.forEach((conv) => {
      const sessionId =
        conv.sessionId || this.generateSessionFromTime(conv.timestamp);

      if (!groups.has(sessionId)) {
        groups.set(sessionId, {
          sessionId,
          messages: [],
          startTime: conv.timestamp,
          endTime: conv.timestamp,
        });
      }

      const group = groups.get(sessionId);
      group.messages.push(conv);
      group.endTime = conv.timestamp;
    });

    // Convert to array and add titles and previews
    return Array.from(groups.values())
      .map((group) => {
        group.title = this.generateConversationTitle(group.messages);
        group.preview = this.generateConversationPreview(group.messages);
        return group;
      })
      .sort((a, b) => new Date(b.startTime) - new Date(a.startTime));
  }

  generateSessionFromTime(timestamp) {
    // Group conversations within 30 minutes of each other
    const time = new Date(timestamp);
    const roundedTime = new Date(
      Math.floor(time.getTime() / (30 * 60 * 1000)) * (30 * 60 * 1000)
    );
    return roundedTime.getTime().toString();
  }

  generateConversationTitle(messages) {
    // Analyze messages to determine main theme
    const allText = messages
      .map((m) => m.userMessage + ' ' + m.aiResponse)
      .join(' ')
      .toLowerCase();

    // Define topic keywords and their titles
    const topics = [
      {
        keywords: ['spotify', 'music', 'streaming', 'arpu'],
        title: 'Spotify Discussion',
      },
      {
        keywords: ['apple', 'aapl', 'iphone', 'india tariff'],
        title: 'Apple Analysis',
      },
      {
        keywords: ['tesla', 'tsla', 'electric', 'elon'],
        title: 'Tesla Insights',
      },
      {
        keywords: ['bp', 'oil', 'discovery', 'brazil'],
        title: 'BP Oil Discovery',
      },
      {
        keywords: ['coinbase', 'crypto', 'bitcoin', 'convertible'],
        title: 'Coinbase & Crypto',
      },
      {
        keywords: ['palantir', 'pltr', 'data', 'analytics'],
        title: 'Palantir Updates',
      },
      {
        keywords: ['rainbow', 'rare earth', 'rbw'],
        title: 'Rainbow Rare Earths',
      },
      {
        keywords: ['market', 'stocks', 'investment', 'portfolio'],
        title: 'Market Discussion',
      },
      {
        keywords: ['chat', 'collective', 'community', 'discussed'],
        title: 'Community Chat Review',
      },
      {
        keywords: ['valuation', 'price', 'movement', 'catalyst'],
        title: 'Stock Analysis',
      },
      { keywords: ['energy', 'nuclear', 'transition'], title: 'Energy Sector' },
      {
        keywords: ['s&p', 'short', 'technical', 'drawdown'],
        title: 'Technical Analysis',
      },
    ];

    // Find the best matching topic
    for (const topic of topics) {
      const matchCount = topic.keywords.filter((keyword) =>
        allText.includes(keyword)
      ).length;
      if (matchCount >= 1) {
        return topic.title;
      }
    }

    // Default title based on first user message
    const firstMessage = messages[0]?.userMessage || '';
    if (firstMessage.length > 30) {
      return firstMessage.substring(0, 30) + '...';
    }

    return firstMessage || 'General Discussion';
  }

  generateConversationPreview(messages) {
    const firstUserMessage = messages[0]?.userMessage || '';
    return firstUserMessage.length > 80
      ? firstUserMessage.substring(0, 80) + '...'
      : firstUserMessage;
  }

  renderConversationMessages(messages) {
    let html = '<div class="message-list">';

    messages.forEach((msg) => {
      const date = new Date(msg.timestamp);
      const time = date.toLocaleTimeString([], {
        hour: '2-digit',
        minute: '2-digit',
      });

      html += `
                <div class="message-exchange">
                    <div class="message-time">${time}</div>
                    <div class="user-message">
                        <strong>You:</strong> ${this.escapeHtml(
                          msg.userMessage
                        )}
                    </div>
                    <div class="ai-message">
                        <strong>Sarah:</strong> ${this.escapeHtml(
                          msg.aiResponse
                        )}
                    </div>
                </div>
            `;
    });

    html += '</div>';
    return html;
  }

  toggleConversationDetails(sessionId) {
    const details = document.getElementById(`details-${sessionId}`);
    const icon = document.querySelector(
      `[data-session-id="${sessionId}"] .expand-icon i`
    );

    if (details.style.display === 'none') {
      details.style.display = 'block';
      icon.classList.remove('fa-chevron-down');
      icon.classList.add('fa-chevron-up');
    } else {
      details.style.display = 'none';
      icon.classList.remove('fa-chevron-up');
      icon.classList.add('fa-chevron-down');
    }
  }

  escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  async exportConversationHistory() {
    try {
      const response = await fetch('/api/conversation-history');
      const data = await response.json();

      if (data.success && data.conversations) {
        const exportData = {
          exportDate: new Date().toISOString(),
          totalConversations: data.conversations.length,
          conversations: data.conversations,
        };

        const blob = new Blob([JSON.stringify(exportData, null, 2)], {
          type: 'application/json',
        });

        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `conversation-history-${
          new Date().toISOString().split('T')[0]
        }.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        this.updateStatus('Conversation history exported successfully!');
      }
    } catch (error) {
      console.error('Error exporting conversation history:', error);
      this.updateStatus('Error exporting conversation history.');
    }
  }

  async clearConversationHistory() {
    if (
      confirm(
        'Are you sure you want to clear all conversation history? This action cannot be undone.'
      )
    ) {
      try {
        const response = await fetch('/api/conversation-history', {
          method: 'DELETE',
        });

        const data = await response.json();

        if (data.success) {
          this.historyContent.innerHTML =
            '<div class="no-history">No conversation history found.</div>';
          this.updateStatus('Conversation history cleared successfully!');
        } else {
          this.updateStatus('Error clearing conversation history.');
        }
      } catch (error) {
        console.error('Error clearing conversation history:', error);
        this.updateStatus('Error clearing conversation history.');
      }
    }
  }

  async continueConversation(sessionId) {
    try {
      // Find the conversation group
      const conversationGroup = this.allConversations.find(
        (group) => group.sessionId === sessionId
      );

      if (!conversationGroup) {
        this.updateStatus('Conversation not found.');
        return;
      }

      // Load the conversation context
      this.loadConversationContext(conversationGroup);

      // Close the history modal
      this.hideHistory();
      this.hideMenu();

      // Show a message indicating the conversation has been resumed
      this.addMessageToDisplay(
        'system',
        `📝 Resumed conversation: "${conversationGroup.title}"`
      );

      // Scroll to bottom to show the resume message
      this.scrollToBottom();

      this.updateStatus(`Conversation resumed: ${conversationGroup.title}`);
    } catch (error) {
      console.error('Error continuing conversation:', error);
      this.updateStatus('Error resuming conversation.');
    }
  }

  loadConversationContext(conversationGroup) {
    // Set the current session ID
    this.currentSessionId = conversationGroup.sessionId;

    // Load conversation history for context
    this.conversationHistory = [];
    conversationGroup.messages.forEach((msg) => {
      this.conversationHistory.push(
        { role: 'user', content: msg.userMessage },
        { role: 'assistant', content: msg.aiResponse }
      );
    });

    // Keep conversation history manageable (last 20 exchanges)
    if (this.conversationHistory.length > 20) {
      this.conversationHistory = this.conversationHistory.slice(-20);
    }

    // Load any lead data from the last message
    const lastMessage =
      conversationGroup.messages[conversationGroup.messages.length - 1];
    if (
      lastMessage &&
      lastMessage.leadData &&
      Object.keys(lastMessage.leadData).length > 0
    ) {
      this.leadData = { ...lastMessage.leadData };
      this.updateLeadDisplay();
    }

    // Update session ID for new messages
    this.sessionId = conversationGroup.sessionId;
  }

  addMessageToDisplay(sender, message) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${sender}-message`;

    if (sender === 'system') {
      messageDiv.innerHTML = `<div class="system-message">${message}</div>`;
      messageDiv.style.textAlign = 'center';
      messageDiv.style.color = '#888';
      messageDiv.style.fontStyle = 'italic';
      messageDiv.style.margin = '10px 0';
    } else {
      const senderLabel = sender === 'user' ? 'You' : 'Sarah';
      messageDiv.innerHTML = `<strong>${senderLabel}:</strong> ${message}`;
    }

    this.conversationDisplay.appendChild(messageDiv);
  }

    scrollToBottom() {
        this.conversationDisplay.scrollTop = this.conversationDisplay.scrollHeight;
    }

    /**
     * Stock Preview Card Methods
     */

    /**
     * Show stock preview card with full stock data
     * @param {Object} stockData - Complete stock object with all data
     */
    showStockPreview(stockData) {
        if (!this.stockPreviewCard || !this.previewCompanyName || !this.previewTicker) {
            console.warn('Stock preview elements not found');
            return;
        }

        // Update basic info
        this.previewCompanyName.textContent = stockData.companyName;
        this.previewTicker.textContent = stockData.ticker;

        // Update price info
        if (this.previewPrice) {
            this.previewPrice.textContent = `$${stockData.price}`;
        }

        // Update price change with color coding
        if (this.previewChange) {
            const changePercent = stockData.changePercent;
            const changeText = changePercent > 0 ? `+${changePercent}%` : `${changePercent}%`;
            this.previewChange.textContent = changeText;

            // Add appropriate class for color
            this.previewChange.classList.remove('positive', 'negative');
            this.previewChange.classList.add(changePercent >= 0 ? 'positive' : 'negative');
        }

        // Update market cap
        if (this.previewMarketCap) {
            this.previewMarketCap.textContent = stockData.marketCap;
        }

        // Update price target
        if (this.previewTarget) {
            this.previewTarget.textContent = `PT: $${stockData.priceTarget}`;
        }

        // Show the card with animation
        this.stockPreviewCard.style.display = 'block';

        // Force reflow to ensure display change is applied
        this.stockPreviewCard.offsetHeight;

        // Add show class for smooth animation
        this.stockPreviewCard.classList.add('show', 'animate-in');
        this.stockPreviewCard.classList.remove('animate-out');

        console.log(`📊 Showing stock preview: ${stockData.companyName} (${stockData.ticker}) - $${stockData.price} (${stockData.changePercent > 0 ? '+' : ''}${stockData.changePercent}%)`);
    }

    /**
     * Hide stock preview card with animation
     */
    hideStockPreview() {
        if (!this.stockPreviewCard) {
            return;
        }

        // Add exit animation
        this.stockPreviewCard.classList.add('animate-out');
        this.stockPreviewCard.classList.remove('show', 'animate-in');

        // Hide after animation completes
        setTimeout(() => {
            this.stockPreviewCard.style.display = 'none';
            this.stockPreviewCard.classList.remove('animate-out');
        }, 300);

        console.log('📊 Hiding stock preview');
    }

    /**
     * Show stock preview for detected stocks during AI speech
     * @param {Array} detectedStocks - Array of detected stock objects
     */
    showStockPreviewForSpeech(detectedStocks) {
        if (!detectedStocks || detectedStocks.length === 0) {
            return;
        }

        // Show the first detected stock (highest confidence)
        const primaryStock = detectedStocks.sort((a, b) =>
            (b.matchDetails?.confidence || 0) - (a.matchDetails?.confidence || 0)
        )[0];

        this.showStockPreview(primaryStock);

        // No auto-hide timeout - will be hidden when speech ends
        console.log('📊 Stock preview will remain visible during entire AI speech');
    }

    /**
     * Handle stock preview during AI speech
     * @param {string} aiResponseText - The AI response text to analyze
     */
    async handleStockPreviewForSpeech(aiResponseText) {
        try {
            // Detect stocks in the AI response
            const detectedStocks = await this.detectStocksInResponse(aiResponseText);

            if (detectedStocks.length > 0) {
                console.log(`📊 Detected ${detectedStocks.length} stock(s) in AI response:`,
                    detectedStocks.map(s => s.ticker).join(', '));

                // Show preview for the detected stocks
                this.showStockPreviewForSpeech(detectedStocks);
            }
        } catch (error) {
            console.error('Error handling stock preview:', error);
        }
    }

    /**
     * Detect stocks in AI response text
     * @param {string} text - AI response text to analyze
     * @returns {Array} Array of detected stocks with full data
     */
    async detectStocksInResponse(text) {
        try {
            if (!text || typeof text !== 'string') {
                return [];
            }

            // Use the backend stock detection service
            const response = await this.authService.makeAuthenticatedRequest(
                '/api/detect-stocks',
                {
                    method: 'POST',
                    body: JSON.stringify({ text }),
                }
            );

            if (!response.ok) {
                console.error('Stock detection API error:', response.status);
                return [];
            }

            const result = await response.json();

            if (result.success && result.stocks) {
                console.log(`📊 Backend detected ${result.count} stock(s) in text: "${result.text}"`);
                console.log('📊 Detected stocks:',
                    result.stocks.map(s => `${s.ticker} (confidence: ${s.matchDetails?.confidence || 'N/A'})`).join(', '));

                // Log match details for debugging
                result.stocks.forEach(stock => {
                    if (stock.matchDetails) {
                        console.log(`📊 ${stock.ticker} matches:`, stock.matchDetails);
                    }
                });

                return result.stocks;
            }

            return [];
        } catch (error) {
            console.error('Error detecting stocks:', error);
            // Fallback to empty array instead of crashing
            return [];
        }
    }

    // Legacy fallback method (keeping for reference)
    async detectStocksInResponseLegacy(text) {
        try {
            // Stock data from our stockData.json
            const stockDatabase = {
                'AAPL': {
                    ticker: 'AAPL',
                    companyName: 'Apple Inc.',
                    price: 189.84,
                    changePercent: 2.34,
                    marketCap: '2.89T',
                    priceTarget: 220.00,
                    sector: 'Technology'
                },
                'TSLA': {
                    ticker: 'TSLA',
                    companyName: 'Tesla, Inc.',
                    price: 248.50,
                    changePercent: -1.87,
                    marketCap: '789.2B',
                    priceTarget: 300.00,
                    sector: 'Consumer Discretionary'
                },
                'NVDA': {
                    ticker: 'NVDA',
                    companyName: 'NVIDIA Corporation',
                    price: 118.75,
                    changePercent: 3.45,
                    marketCap: '2.92T',
                    priceTarget: 140.00,
                    sector: 'Technology'
                },
                'SPOT': {
                    ticker: 'SPOT',
                    companyName: 'Spotify Technology S.A.',
                    price: 342.18,
                    changePercent: 1.23,
                    marketCap: '68.5B',
                    priceTarget: 380.00,
                    sector: 'Communication Services'
                },
                'META': {
                    ticker: 'META',
                    companyName: 'Meta Platforms, Inc.',
                    price: 512.33,
                    changePercent: 0.89,
                    marketCap: '1.31T',
                    priceTarget: 580.00,
                    sector: 'Communication Services'
                },
                'PLTR': {
                    ticker: 'PLTR',
                    companyName: 'Palantir Technologies Inc.',
                    price: 28.45,
                    changePercent: 4.67,
                    marketCap: '62.8B',
                    priceTarget: 35.00,
                    sector: 'Technology'
                },
                'COIN': {
                    ticker: 'COIN',
                    companyName: 'Coinbase Global, Inc.',
                    price: 195.67,
                    changePercent: -2.14,
                    marketCap: '48.2B',
                    priceTarget: 250.00,
                    sector: 'Financial Services'
                },
                'QCOM': {
                    ticker: 'QCOM',
                    companyName: 'QUALCOMM Incorporated',
                    price: 168.92,
                    changePercent: 1.56,
                    marketCap: '188.4B',
                    priceTarget: 190.00,
                    sector: 'Technology'
                }
            };

            const stockPatterns = [
                { pattern: /\b(AAPL|Apple Inc\.?|Apple)\b/gi, ticker: 'AAPL' },
                { pattern: /\b(TSLA|Tesla Inc\.?|Tesla)\b/gi, ticker: 'TSLA' },
                { pattern: /\b(NVDA|NVIDIA|Nvidia)\b/gi, ticker: 'NVDA' },
                { pattern: /\b(SPOT|Spotify)\b/gi, ticker: 'SPOT' },
                { pattern: /\b(META|Meta|Facebook)\b/gi, ticker: 'META' },
                { pattern: /\b(PLTR|Palantir)\b/gi, ticker: 'PLTR' },
                { pattern: /\b(COIN|Coinbase)\b/gi, ticker: 'COIN' },
                { pattern: /\b(QCOM|Qualcomm)\b/gi, ticker: 'QCOM' }
            ];

            const detectedStocks = [];

            stockPatterns.forEach(({ pattern, ticker }) => {
                if (pattern.test(text)) {
                    const stockData = stockDatabase[ticker];
                    if (stockData) {
                        detectedStocks.push({
                            ...stockData,
                            matchDetails: { confidence: 0.8 }
                        });
                    }
                }
            });

            // Remove duplicates
            const uniqueStocks = detectedStocks.filter((stock, index, self) =>
                index === self.findIndex(s => s.ticker === stock.ticker)
            );

            return uniqueStocks;
        } catch (error) {
            console.error('Error detecting stocks:', error);
            return [];
        }
    }
}

// Initialize the app when the page loads
let app;
document.addEventListener('DOMContentLoaded', () => {
  app = new VoiceLeadApp();
});

// Global function for debugging microphone button visibility
window.debugMicButton = function () {
  if (app && app.checkButtonVisibility) {
    app.checkButtonVisibility();
  } else {
    console.log(
      'App not initialized or checkButtonVisibility method not available'
    );
  }
};
